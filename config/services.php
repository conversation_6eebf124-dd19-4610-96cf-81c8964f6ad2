<?php

use App\Enums\InterScope;

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'inter' => [
        'base_uri' => env('INTER_BASE_URI', 'https://cdpj-sandbox.partners.uatinter.co'),
        'client_id' => env('INTER_CLIENT_ID'),
        'client_secret' => env('INTER_CLIENT_SECRET'),
        'cert_path' => env('INTER_CERT_PATH'), // absolute path to certificate .crt file
        'key_path' => env('INTER_KEY_PATH'),   // absolute path to key .key file
        'token_cache_ttl' => env('INTER_TOKEN_CACHE_TTL', 3500),//100s less than the informed on api docs
        'query_max_date_interval'=> env('INTER_QUERY_MAX_DATE_INTERVAL', 90),//in days
        'scope' => env('INTER_SCOPE', implode(' ', [
            InterScope::BoletoCobrancaRead->value,
            InterScope::BoletoCobrancaWrite->value,
            InterScope::PagamentoBoletoRead->value,
            InterScope::PagamentoBoletoWrite->value,
            InterScope::ExtratoRead->value,
            InterScope::PagamentoDarfWrite->value,
            InterScope::PagamentoPixWrite->value,
            InterScope::PagamentoPixRead->value,
        ])),
    ],

];
