### Listar pagamentos (boleto-payments)
@host = http://localhost:8000/api
@bank = inter

### Executar um pagamento via DAR
# @name executeBoletoPayment

POST {{host}}/darf-payments
Content-Type: application/json
Accept: application/json

{
  "bank": "{{bank}}",
  "recipientsDocument": "***********",
  "receitaCode": "0220",
  "dueDate": "2025-09-01",
  "description": "Pagamento DARF Janeiro",
  "companyName": "Minha Empresa",
  "companyPhone": "************",
  "assessmentPeriod": "2020-01-31",
  "amount": 100.00,
  "feeValue": 10.00,
  "interestValue": 10.00,
  "reference": "**********"
}

### Listar pagamentos DARF
GET {{host}}/darf-payments?bank={{bank}}&solicitationCode=******************************123456&receitaCode=0220&startDate=2025-01-01&endDate=2025-07-01
Accept: application/json
