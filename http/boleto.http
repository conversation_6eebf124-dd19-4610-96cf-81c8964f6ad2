### Listar pagamentos (boleto-payments)
@host = http://localhost:8000/api
@bank = inter

# @name search boleto-payments

### pesquisa um boleto pelo código de barras
GET {{host}}/boleto-payments?bank={{bank}}&barCode=03395988500000666539201493990000372830030102
Accept: application/json

### pesquisa um boleto pelo código de transação
GET {{host}}/boleto-payments?bank={{bank}}&transactionCode=2aaaeef3-6aca-4550-96fd-44c9798c5832
Accept: application/json

### Executar um pagamento (charge) via boleto
# @name executeBoletoPayment

POST {{host}}/boleto-payments
Content-Type: application/json
Accept: application/json

{
  "bank": "{{bank}}",
  "barCode": "03395988500000666539201493990000372830030102",
  "amount": "12.34",
  "paymentDate": "2025-08-29",
  "dueDate": "2025-09-01",
  "recipientsDocument": "***********"
}

### Cancelar um pagamento (charge) via boleto  - agendado

@transactionId = 2aaaeef3-6aca-4550-96fd-44c9798c5832
DELETE {{host}}/boleto-payments/{{transactionId}}?bank={{bank}}
Content-Type: application/json
Accept: application/json
