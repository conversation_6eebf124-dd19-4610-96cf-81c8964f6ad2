### Listar pagamentos pix
@host = http://localhost:8000/api
@bank = inter

# @name search pix

### Pesquisa pagamentos PIX por um intervalo de datas
GET {{host}}/pix-payments?bank={{bank}}&startDate=2024-01-01&endDate=2024-01-31
Accept: application/json

###

### Executar um pagamento via PIX
# @name executePixPayment

POST {{host}}/pix-payments
Content-Type: application/json
Accept: application/json

{
  "bank": "{{bank}}",
  "amount": 0.01,
  "description": "Pagamento de teste via API",
  "destination": {
    "keyType": "EMAIL",
    "key": "<EMAIL>"
  }
}
