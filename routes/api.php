<?php

use App\Http\Controllers\BoletoPaymentsController;
use App\Http\Controllers\PixPaymentsController;
use App\Http\Controllers\StatementController;
use App\Http\Controllers\BalanceController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DarfPaymentsController;


Route::get('/', fn() => response()->json(['status' => 'ok']));

Route::apiResource('statement', StatementController::class)->only(['index']);
Route::apiResource('balance', BalanceController::class)->only(['index']);
Route::apiResource('boleto-payments', BoletoPaymentsController::class)->only(['index', 'store', 'destroy']);
Route::apiResource('darf-payments', DarfPaymentsController::class)->only(['index', 'store']);
Route::apiResource('pix-payments', PixPaymentsController::class)->only(['index', 'store']);
