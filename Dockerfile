# Use the official PHP image with FPM
FROM php:8.4-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    nginx \
    curl \
    git \
    unzip \
    libzip-dev \
    acl \
    libonig-dev \
    supervisor \
    && docker-php-ext-install zip pdo pdo_mysql bcmath \
    && docker-php-ext-enable opcache \
    && rm -rf /var/lib/apt/lists/*

# Install Composer globally
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

WORKDIR /var/www/html
RUN git config --global --add safe.directory /var/www/html

# Now copying the rest of the application
COPY . .

# set laravel permissions (applies to image layer only; bind mounts will override)
RUN mkdir -p /var/www/html/storage /var/www/html/bootstrap/cache \
    && chown -R www-data:www-data /var/www/html \
    && chmod -R 775 /var/www/html/storage /var/www/html/bootstrap/cache

RUN #composer install --no-interaction --prefer-dist --no-dev
RUN composer install --no-interaction --prefer-dist

# copy entrypoint that will fix perms after bind mount at runtime
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

EXPOSE 80

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]