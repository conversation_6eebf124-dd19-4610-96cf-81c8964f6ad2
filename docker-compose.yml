services:
  app:
    build:
      context: .
      dockerfile: ./Dockerfile
    container_name: banking-ms
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d/site.conf:/etc/nginx/conf.d/site.conf
      - ./docker/supervisor/supervisord.conf:/etc/supervisor/conf.d/supervisord.conf
    ports:
      - "8000:80"
    networks:
      - local

networks:
  local:
    external: true