worker_processes auto;
user www-data;

error_log /var/log/nginx/error.log;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    # Mime type definitions
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logs
    access_log /var/log/nginx/access.log;
    sendfile on;

    # Keep connections open
    keepalive_timeout 65;

    # Include the site-specific configuration(s)
    include /etc/nginx/conf.d/*.conf;
}
