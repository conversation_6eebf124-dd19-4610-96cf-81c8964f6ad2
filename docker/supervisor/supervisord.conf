[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/var/run/supervisord.pid
user=root

[program:nginx]
command=nginx -g 'daemon off;'
autostart=true
autorestart=true
startsecs=10
startretries=3
priority=10
redirect_stderr=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0

[program:php-fpm]
command=php-fpm --nodaemonize --fpm-config /usr/local/etc/php-fpm.conf
autostart=true
autorestart=true
startretries=3
priority=5
redirect_stderr=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0