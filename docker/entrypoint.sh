#!/usr/bin/env sh
set -e

cd /var/www/html

# Ensure Laravel writable dirs exist
mkdir -p storage/framework/{cache,sessions,testing,views} \
         storage/logs \
         bootstrap/cache

# Windows bind mounts often ignore ownership; use permissive perms for dev
chmod -R 775 storage || true
chmod -R 775 bootstrap/cache || true

# If previous builds left root-owned cache files, clear them safely
php artisan view:clear >/dev/null 2>&1 || true
php artisan cache:clear >/dev/null 2>&1 || true
php artisan config:clear >/dev/null 2>&1 || true

# Start supervisord (nginx + php-fpm)
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf