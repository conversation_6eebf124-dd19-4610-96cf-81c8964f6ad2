<?php

$payload = [
    'bank' => 'inter',
    'startDate' => '2025-01-01',
    'endDate' => '2025-02-01',
];

it(
    'should return a bank statement successfully',
    fn()
        => $this
        ->getJson("/api/statement?" . http_build_query($payload))
        ->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'transacoes' => [
                    '*' => [
                        'dataEntrada',
//                  'tipoTransacao',
                        'tipoOperacao',
                        'valor',
//                  'titulo',
//                  'descricao',
                    ],
                ],
            ],
        ]),
);


test(
    'gets validation errors for missing or invalid statement fields',
    fn(array $query, array $expectedErrors)
        => $this
        ->getJson("/api/statement?" . http_build_query($query))
        ->assertStatus(422)
        ->assertJsonValidationErrors($expectedErrors),
)->with([
    'interval greater than 90 days' => [
        'query' => [...$payload, 'startDate' => '2025-01-01', 'endDate' => '2025-05-01'],
        'expectedErrors' => ['endDate' => 'Maximum date interval is 90 days.'],
    ],
    'missing bank' => [
        'query' => [...$payload, 'bank' => null],
        'expectedErrors' => ['bank' => 'The bank field is required.'],
    ],
    'invalid bank' => [
        'query' => [...$payload, 'bank' => 'nullbank'],
        'expectedErrors' => ['bank' => 'The selected bank is invalid.'],
    ],
    'missing startDate' => [
        'query' => [...$payload, 'startDate' => null],
        'expectedErrors' => ['startDate' => 'The start date field is required.'],
    ],
    'invalid startDate' => [
        'query' => [...$payload, 'startDate' => 'invalid-date'],
        'expectedErrors' => [
            "startDate" => ["The start date field must match the format Y-m-d."],
            "endDate" => ["The end date field must be a date after or equal to start date."],
        ],
    ],
    'missing endDate' => [
        'query' => [...$payload, 'endDate' => null],
        'expectedErrors' => ['endDate' => 'The end date field is required.'],
    ],
    'invalid endDate' => [
        'query' => [...$payload, 'endDate' => 'invalid-date'],
        'expectedErrors' => ['endDate' => 'The end date field must match the format Y-m-d.'],
    ],
    'end date before start date' => [
        'query' => [...$payload, 'startDate' => '2025-01-10', 'endDate' => '2025-01-01'],
        'expectedErrors' => ['endDate' => 'The end date field must be a date after or equal to start date.'],
    ],
]);