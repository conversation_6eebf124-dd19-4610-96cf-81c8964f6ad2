<?php

use Carbon\Carbon;

$transactionId = null;

it('should make a boleto payment so it can be canceled and we have a valid transactionId', function () use (&$transactionId) {
    $response = $this
        ->postJson(
            '/api/boleto-payments',
            [
                'bank' => 'inter',
                'barCode' => '03395988500000666539201493990000372830030102',
                'amount' => 100.00,
                'paymentDate' => Carbon::now()->addDay()->toDateString(),
                'dueDate' => Carbon::now()->addDays(3)->toDateString(),
                'recipientsDocument' => '***********',
            ]
        )
        ->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'success',
                'transaction_code',
                'message',
                'status',
                'response_payload',
            ],
        ]);

    $transactionId = $response->json('data.transaction_code');

    return $transactionId;
});


$payload = [
    'bank' => 'inter',
    'transactionId' => $transactionId,
];

it(
    'should cancel a boleto payment successfully',
    fn()
        => $this
        ->deleteJson("/api/boleto-payments?" . http_build_query($payload))
        ->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'success',
                'message',
            ],
        ]),
);

test(
    'gets validation errors for missing or invalid boleto payment fields',
    fn(array $query, array $expectedErrors)
        => $this
        ->deleteJson("/api/boleto-payments?" . http_build_query($query))
        ->assertStatus(422)
        ->assertJsonValidationErrors($expectedErrors),
)->with([
    'missing bank' => [
        'query' => [...$payload, 'bank' => null],
        'expectedErrors' => ['bank' => ['The bank field is required.'],],
    ],
    'invalid bank' => [
        'query' => [...$payload, 'bank' => 'nullbank'],
        'expectedErrors' => ["bank" => ["The selected bank is invalid."]],
    ],
    'missing transactionId' => [
        'query' => [...$payload, 'transactionId' => null],
        'expectedErrors' => ['transactionId' => ['The transaction id field is required.']],
    ],
]);