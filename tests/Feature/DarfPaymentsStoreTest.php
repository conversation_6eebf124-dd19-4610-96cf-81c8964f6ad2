<?php

$payload = [
    'bank' => 'inter',
    'recipientsDocument' => '***********',
    'receitaCode' => '0220',
    'dueDate' => '2025-09-01',
    'description' => 'Pagamento DARF Janeiro',
    'companyName' => 'Minha Empresa',
    'companyPhone' => '************',
    'assessmentPeriod' => '2020-01-31',
    'amount' => 100.00,
    'feeValue' => 10.00,
    'interestValue' => 10.00,
    'reference' => '**********',
];


it('should execute a darf payment successfully', function () use ($payload) {

    $this
        ->postJson('/api/darf-payments', $payload)
        ->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'success',
                'solicitation_code',
                'message',
                'status',
                'response_payload',
            ],
        ]);
});

test(
    'gets validation errors for missing or invalid darf payment fields',
    function (array $payload, array $expectedErrors) {
        $this
            ->postJson('/api/darf-payments', $payload)
            ->assertStatus(422)
            ->assertJsonValidationErrors($expectedErrors);
    },
)->with([
    'missing bank' => [
        'payload' => [...$payload, 'bank' => null],
        'expectedErrors' => ['bank' => 'The bank field is required.'],
    ],
    'invalid bank' => [
        'payload' => [...$payload, 'bank' => 'nullbank'],
        'expectedErrors' => ['bank' => 'The selected bank is invalid.'],
    ],
    'missing recipientsDocument' => [
        'payload' => [...$payload, 'recipientsDocument' => null],
        'expectedErrors' => ['recipientsDocument' => 'The recipients document field is required.'],
    ],
    'invalid recipientsDocument' => [
        'payload' => [...$payload, 'recipientsDocument' => '123'],
        'expectedErrors' => ['recipientsDocument' => 'The recipients document field must be a valid CPF or CNPJ.'],
    ],
    'missing receitaCode' => [
        'payload' => [...$payload, 'receitaCode' => null],
        'expectedErrors' => ['receitaCode' => 'The receita code field is required.'],
    ],
    'invalid receitaCode' => [
        'payload' => [...$payload, 'receitaCode' => '123'],
        'expectedErrors' => ['receitaCode' => 'The receita code field must be 4 digits.'],
    ],
    'missing dueDate' => [
        'payload' => [...$payload, 'dueDate' => null],
        'expectedErrors' => ['dueDate' => 'The due date field is required.'],
    ],
    'invalid dueDate' => [
        'payload' => [...$payload, 'dueDate' => 'invalid-date'],
        'expectedErrors' => ['dueDate' => 'The due date field must match the format Y-m-d.'],
    ],
    'missing description' => [
        'payload' => [...$payload, 'description' => null],
        'expectedErrors' => ['description' => 'The description field is required.'],
    ],
    'too long description' => [
        'payload' => [...$payload, 'description' => str_repeat('a', 1001)],
        'expectedErrors' => ['description' => 'The description field must not be greater than 1000 characters.'],
    ],
    'missing companyName' => [
        'payload' => [...$payload, 'companyName' => null],
        'expectedErrors' => ['companyName' => 'The company name field is required.'],
    ],
    'too long companyName' => [
        'payload' => [...$payload, 'companyName' => str_repeat('a', 101)],
        'expectedErrors' => ['companyName' => 'The company name field must not be greater than 100 characters.'],
    ],
    'too long companyPhone' => [
        'payload' => [...$payload, 'companyPhone' => str_repeat('1', 21)],
        'expectedErrors' => ['companyPhone' => 'The company phone field must not be greater than 20 characters.'],
    ],
    'missing assessmentPeriod' => [
        'payload' => [...$payload, 'assessmentPeriod' => null],
        'expectedErrors' => ['assessmentPeriod' => 'The assessment period field is required.'],
    ],
    'invalid assessmentPeriod' => [
        'payload' => [...$payload, 'assessmentPeriod' => 'invalid-date'],
        'expectedErrors' => ['assessmentPeriod' => 'The assessment period field must match the format Y-m-d.'],
    ],
    'missing amount' => [
        'payload' => [...$payload, 'amount' => null],
        'expectedErrors' => ['amount' => 'The amount field is required.'],
    ],
    'negative amount' => [
        'payload' => [...$payload, 'amount' => -100.00],
        'expectedErrors' => ['amount' => 'The amount field must be greater than 0.'],
    ],
    'invalid amount' => [
        'payload' => [...$payload, 'amount' => 'invalid-amount'],
        'expectedErrors' => ['amount' => 'The amount field must be a number.'],
    ],
    'negative feeValue' => [
        'payload' => [...$payload, 'feeValue' => -100.00],
        'expectedErrors' => ['feeValue' => 'The fee value field must be greater than 0.'],
    ],
    'invalid feeValue' => [
        'payload' => [...$payload, 'feeValue' => 'invalid-fee'],
        'expectedErrors' => ['feeValue' => 'The fee value field must be a number.'],
    ],
    'negative interestValue' => [
        'payload' => [...$payload, 'interestValue' => -100.00],
        'expectedErrors' => ['interestValue' => 'The interest value field must be greater than 0.'],
    ],
    'invalid interestValue' => [
        'payload' => [...$payload, 'interestValue' => 'invalid-interest'],
        'expectedErrors' => ['interestValue' => 'The interest value field must be a number.'],
    ],
    'missing reference' => [
        'payload' => [...$payload, 'reference' => null],
        'expectedErrors' => ['reference' => 'The reference field is required.'],
    ],
    'invalid reference' => [
        'payload' => [...$payload, 'reference' => str_repeat('a', 31)],
        'expectedErrors' => ['reference' => 'The reference field must not be greater than 30 characters.'],
    ],
]);
