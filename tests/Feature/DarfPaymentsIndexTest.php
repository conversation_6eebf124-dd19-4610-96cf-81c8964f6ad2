<?php

$payload = [
    'bank' => 'inter',
    'solicitationCode' => '123456789012345678901234567890123456',
    'receitaCode' => '0220',
    'startDate' => '2025-01-01',
    'endDate' => '2025-02-01',
];

$expectedStructure = [
    'data' => [
        '*' => [
            "codigoSolicitacao",
            "tipoDarf",
            "valor",
            "valorMulta",
            "valorJuros",
            "valorTotal",
            "tipo",
            "periodoApuracao",
            "dataPagamento",
            "referencia",
            "dataVencimento",
            "codigoReceita",
            "statusPagamento",
            "dataInclusao",
            "cnpjCpf",
            "aprovacoesNecessarias",
            "aprovacoesRealizadas",
        ],
    ],
];

it(
    'should return a list of darf payments successfully',
    fn()
        => $this
        ->getJson("/api/darf-payments?" . http_build_query($payload))
        ->assertStatus(200)
        ->assertJsonStructure($expectedStructure),
);

test(
    'gets validation errors for missing or invalid darf payments fields',
    fn(array $query, array $expectedErrors)
        => $this
        ->getJson("/api/darf-payments?" . http_build_query($query))
        ->assertStatus(422)
        ->assertJsonValidationErrors($expectedErrors),
)->with([
    'missing bank' => [
        'query' => [...$payload, 'bank' => null],
        'expectedErrors' => ['bank' => ['The bank field is required.']],
    ],
    'invalid bank' => [
        'query' => [...$payload, 'bank' => 'nullbank'],
        'expectedErrors' => ["bank" => ["The selected bank is invalid."]],
    ],
    'invalid solicitationCode' => [
        'query' => [...$payload, 'solicitationCode' => 'invalid-solicitation-code'],
        'expectedErrors' => ['solicitationCode' => ['The solicitation code field must be 36 digits.']],
    ],
    'invalid receitaCode' => [
        'query' => [...$payload, 'receitaCode' => 'invalid-receita-code'],
        'expectedErrors' => ['receitaCode' => ['The receita code field must be 4 digits.']],
    ],
    'invalid startDate' => [
        'query' => [...$payload, 'startDate' => 'invalid-date'],
        'expectedErrors' => ['startDate' => ['The start date field must match the format Y-m-d.']],
    ],
    'invalid endDate' => [
        'query' => [...$payload, 'endDate' => 'invalid-date'],
        'expectedErrors' => ['endDate' => ['The end date field must match the format Y-m-d.']],
    ],
    'endDate before startDate' => [
        'query' => [...$payload, 'startDate' => '2025-01-10', 'endDate' => '2025-01-01'],
        'expectedErrors' => ['endDate' => ['The end date field must be a date after or equal to start date.']],
    ],
    'interval greater than 90 days' => [
        'query' => [...$payload, 'startDate' => '2025-01-01', 'endDate' => '2025-05-01'],
        'expectedErrors' => ['endDate' => ['Maximum date interval is 90 days.']],
    ],
]);
