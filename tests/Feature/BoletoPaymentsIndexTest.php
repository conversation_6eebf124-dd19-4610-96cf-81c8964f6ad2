<?php

$payload = [
    'bank' => 'inter',
    'startDate' => '2025-01-01',
    'endDate' => '2025-02-01',
    'filterby' => 'INCLUSAO',
];

test(
    'should return a list of boleto payments successfully',
    fn($payload)
        => $this
        ->getJson("/api/boleto-payments?" . http_build_query($payload))
        ->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'codigoTransacao',
                    'dataPagamento',
//                    'dataVencimento',
                    'valor',
                    'statusPagamento',
                ],
            ],
        ]),
)->with([
    'all fields' => [
        'payload' => $payload,
    ],
    'filter by barCode' => [
        'payload' => [...$payload, 'barCode' => '03395988500000666539201493990000372830030102'],
    ],
    'filter by transactionCode' => [
        'payload' => [...$payload, 'transactionCode' => 'a928f403-0076-419b-9c99-432d52083d0a'],
    ],
    'missing date' => [
        'payload' => [...$payload, 'startDate' => null, 'endDate' => null],
    ],
    'differente filterby' =>[
        'payload' => [...$payload, 'filterby' => 'PAGAMENTO '],
    ],
]);

test(
    'gets validation errors for missing or invalid boleto payments fields',
    fn(array $query, array $expectedErrors)
        => $this
        ->getJson("/api/boleto-payments?" . http_build_query($query))
        ->assertStatus(422)
        ->assertJsonValidationErrors($expectedErrors),
)->with([
    'missing bank' => [
        'query' => [...$payload, 'bank' => null],
        'expectedErrors' => ['bank' => ['The bank field is required.']],
    ],
    'invalid bank' => [
        'query' => [...$payload, 'bank' => 'nullbank'],
        'expectedErrors' => ["bank" => ["The selected bank is invalid."]],
    ],
    'invalid startDate' => [
        'query' => [...$payload, 'startDate' => 'invalid-date'],
        'expectedErrors' => ['startDate' => ['The start date field must match the format Y-m-d.']],
    ],
    'missing startDate' => [
        'query' => [...$payload, 'startDate' => null],
        'expectedErrors' => ['startDate' => ['The start date field is required when end date is present.']],
    ],
    'missing endDate' => [
        'query' => [...$payload, 'endDate' => null],
        'expectedErrors' => ['endDate' => ['The end date field is required when start date is present.']],
    ],
    'invalid endDate' => [
        'query' => [...$payload, 'endDate' => 'invalid-date'],
        'expectedErrors' => ['endDate' => ['The end date field must match the format Y-m-d.']],
    ],
    'endDate before startDate' => [
        'query' => [...$payload, 'startDate' => '2025-01-10', 'endDate' => '2025-01-01'],
        'expectedErrors' => ['endDate' => ['The end date field must be a date after or equal to start date.']],
    ],
    'interval greater than 90 days' => [
        'query' => [...$payload, 'startDate' => '2025-01-01', 'endDate' => '2025-05-01'],
        'expectedErrors' => ['endDate' => ['Maximum date interval is 90 days.']],
    ],
    'missing filterby' => [
        'query' => [...$payload, 'filterby' => null],
        'expectedErrors' => ['filterby' => ['The filterby field is required when start date is present.']],
    ],
    'invalid filterby' => [
        'query' => [...$payload, 'filterby' => 'invalid-filterby'],
        'expectedErrors' => ['filterby' => ['The selected filterby is invalid.']],
    ],
    'invalid transactionCode' => [
        'query' => [...$payload, 'transactionCode' => 'invalid-transaction-code'],
        'expectedErrors' => ['transactionCode' => ['The transaction code field must be 36 characters.']],
    ],
]);