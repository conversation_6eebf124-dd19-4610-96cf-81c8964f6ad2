<?php

use Carbon\Carbon;

it('should execute a boleto payment successfully', function () {
    $payload = [
        'bank' => 'inter',
        'barCode' => '03395988500000666539201493990000372830030102',
        'amount' => 100.00,
        'paymentDate' => Carbon::now()->addDay()->toDateString(),
        'dueDate' => Carbon::now()->addDays(3)->toDateString(),
        'recipientsDocument' => '***********',
    ];

    $this
        ->postJson('/api/boleto-payments', $payload)
        ->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'success',
                'transaction_code',
                'message',
                'status',
                'response_payload',
            ],
        ]);
});


it(
    'should return validation errors for missing or invalid boleto payment fields',
    function (array $payload, array $expectedErrors) {
        $this
            ->postJson('/api/boleto-payments', $payload)
            ->assertStatus(422)
            ->assertJsonValidationErrors($expectedErrors);
    },
)->with([
    'missing bank' => [
        'payload' => [
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => 100.00,
            'recipientsDocument' => '***********',
            'paymentDate' => Carbon::now()->toDateString(),
            'dueDate' => Carbon::now()->toDateString(),
        ],
        'expectedErrors' => ['bank' => 'The bank field is required.'],
    ],
    'invalid bank' => [
        'payload' => [
            'bank' => 'nullbank',
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => 100.00,
            'recipientsDocument' => '***********',
            'paymentDate' => Carbon::now()->toDateString(),
            'dueDate' => Carbon::now()->toDateString(),
        ],
        'expectedErrors' => ['bank' => 'The selected bank is invalid.'],
    ],
    'missing barCode' => [
        'payload' => [
            'bank' => 'inter',
            'amount' => 100.00,
            'recipientsDocument' => '***********',
            'dueDate' => '2025-09-01',
        ],
        'expectedErrors' => ['barCode' => 'The bar code field is required.'],
    ],
    'invalid barCode' => [
        'payload' => [
            'bank' => 'inter',
            'barCode' => '123',
            'amount' => 100.00,
            'recipientsDocument' => '***********',
            'dueDate' => '2025-09-01',
        ],
        'expectedErrors' => ['barCode' => 'The bar code field must be between 44 and 48 digits.'],
    ],
    'missing amount' => [
        'payload' => [
            'bank' => 'inter',
            'recipientsDocument' => '***********',
            'barCode' => '03395988500000666539201493990000372830030102',
            'dueDate' => '2025-09-01',
        ],
        'expectedErrors' => ['amount' => 'The amount field is required.'],
    ],
    'invalid amount' => [
        'payload' => [
            'bank' => 'inter',
            'recipientsDocument' => '***********',
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => -100.00,
            'dueDate' => '2025-09-01',
        ],
        'expectedErrors' => ['amount' => 'The amount field must be at least 0.01.'],
    ],
    'missing dueDate' => [
        'payload' => [
            'bank' => 'inter',
            'recipientsDocument' => '***********',
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => 100.00,
        ],
        'expectedErrors' => ['dueDate' => 'The due date field is required.'],
    ],
    'due date before today' => [
        'payload' => [
            'bank' => 'inter',
            'recipientsDocument' => '***********',
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => 100.00,
            'dueDate' => Carbon::now()->subDay()->toDateString(),
            'paymentDate' => Carbon::now()->toDateString(),
        ],
        'expectedErrors' => ['dueDate' => 'The due date field must be a date after or equal to payment date.'],
    ],
    'due date before paymentDate' => [
        'payload' => [
            'bank' => 'inter',
            'recipientsDocument' => '***********',
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => 100.00,
            'paymentDate' => Carbon::now()->addDays(30)->toDateString(),
            'dueDate' => Carbon::now()->addDays(10)->toDateString(),
        ],
        'expectedErrors' => ['dueDate' => 'The due date field must be a date after or equal to payment date.'],
    ],
    'invalid dueDate' => [
        'payload' => [
            'bank' => 'inter',
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => 100.00,
            'recipientsDocument' => '***********',
            'dueDate' => 'invalid-date',
        ],
        'expectedErrors' => ['dueDate' => 'The due date field must match the format Y-m-d.'],
    ],
    'missing recipientsDocument' => [
        'payload' => [
            'bank' => 'inter',
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => 100.00,
            'paymentDate' => Carbon::now()->toDateString(),
            'dueDate' => Carbon::now()->toDateString(),
        ],
        'expectedErrors' => ['recipientsDocument' => 'The recipients document field is required.'],
    ],
    'invalid recipientsDocument' => [
        'payload' => [
            'bank' => 'inter',
            'barCode' => '03395988500000666539201493990000372830030102',
            'amount' => 100.00,
            'paymentDate' => Carbon::now()->toDateString(),
            'dueDate' => Carbon::now()->toDateString(),
            'recipientsDocument' => '123',
        ],
        'expectedErrors' => ['recipientsDocument' => 'The recipients document field must be 11 digits.'],
    ],
]);
