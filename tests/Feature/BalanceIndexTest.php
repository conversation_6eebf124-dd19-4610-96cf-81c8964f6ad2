<?php

$payload = [
    'bank' => 'inter',
    'date' => '2025-03-01',
];

it('gets bank balance successfully', fn()
    => $this
    ->getJson("/api/balance?" . http_build_query($payload))
    ->assertStatus(200)
    ->assertJsonStructure([
        'data' => [
            'disponivel',
        ],
    ]));

test(
    'gets validation errors',
    fn(array $query, array $expectedErrors)
        => $this
        ->getJson("/api/balance?" . http_build_query($query))
        ->assertStatus(422)
        ->assertJsonValidationErrors($expectedErrors),
)->with([
    'missing bank' => [
        'query' => [...$payload, 'bank' => null],
        'expectedErrors' => ['bank' => 'The bank field is required.'],
    ],
    'invalid bank' => [
        'query' => [...$payload, 'bank' => 'nullbank'],
        'expectedErrors' => ["bank" => "The selected bank is invalid."],
    ],
    'missing date' => [
        'query' => [...$payload, 'date' => null],
        'expectedErrors' => ['date' => 'The date field is required.'],
    ],
    'invalid date' => [
        'query' => [...$payload, 'date' => 'invalid-date'],
        'expectedErrors' => ['date' => 'The date field must match the format Y-m-d.'],
    ],
]);