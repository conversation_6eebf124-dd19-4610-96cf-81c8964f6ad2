<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\Client\Response;
use Throwable;

class BankRequestException extends Exception
{
    public readonly ?Response $response;

    public function __construct(
        string $message = "",
        int $code = 0,
        ?Throwable $previous = null,
        ?Response $response = null,
    ) {
        parent::__construct($message, $code, $previous);
        $this->response = $response;
    }

    public function getResponse(): ?Response
    {
        return $this->response;
    }

    public function getDetails(): ?array
    {
        $details = $this->response?->body();

        if (!json_validate($this->response?->body())) {
            return [print_r($details,true)];
        }

        return json_decode($details, true);
    }
}