<?php

namespace App\Exceptions;

use Illuminate\Http\Client\Response;
use Throwable;

class InterApiException extends BankRequestException
{

    public function __construct(Response $response, string $message = "", ?Throwable $previous = null)
    {
        $message = $message ?: "Inter API request failed with status {$response->status()}";

        parent::__construct($message, $response->status(), $previous, $response);
    }

}


