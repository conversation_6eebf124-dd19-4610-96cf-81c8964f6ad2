<?php

namespace App\Providers;

use App\Services\Banking\BankingService;
use App\Services\Banking\Banks\BradescoService;
use App\Services\Banking\Banks\InterService;
use App\Services\Banking\Banks\SantanderService;
use Illuminate\Support\ServiceProvider;

class BankingServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(BankingService::class, function ($app) {
            return new BankingService([
                'inter' => InterService::class,
                'bradesco' => BradescoService::class,
                'santander' => SantanderService::class,
            ]);
        });
    }
}
