<?php

namespace App\Http\Controllers;

use App\Http\Requests\BoletoPaymentsDestroyRequest;
use App\Http\Requests\BoletoPaymentStoreRequest;
use App\Http\Requests\BoletoPaymentsIndexRequest;
use App\Services\Banking\BankingService;
use Illuminate\Http\JsonResponse;

class BoletoPaymentsController extends Controller
{
    public function __construct(private readonly BankingService $banking) {}

    public function store(BoletoPaymentStoreRequest $request): JsonResponse
    {
        return $this->execute(
            fn() => $this->banking->executeBoletoPayment($request->get('bank'), $request->validated()),
        );
    }

    public function index(BoletoPaymentsIndexRequest $request): JsonResponse
    {
        return $this->execute(
            fn() => $this->banking->getBoletoPayments($request->get('bank'), $request->validated()),
        );
    }

    public function destroy(BoletoPaymentsDestroyRequest $request): JsonResponse
    {
        $validated = $request->validated();
        return $this->execute(
            fn()
                => $this->banking->cancelBoletoPayment(
                $validated['bank'],
                $validated['transactionCode'],
            ),
        );
    }
}