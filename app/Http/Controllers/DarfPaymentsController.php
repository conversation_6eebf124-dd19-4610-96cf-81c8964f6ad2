<?php

namespace App\Http\Controllers;

use App\Http\Requests\DarfPaymentStoreRequest;
use App\Http\Requests\DarfPaymentsIndexRequest;
use App\Services\Banking\BankingService;
use Illuminate\Http\JsonResponse;

class DarfPaymentsController extends Controller
{

    public function __construct(private readonly BankingService $banking) {}

    public function store(DarfPaymentStoreRequest $request): JsonResponse
    {
        return $this->execute(
            fn()
                => $this->banking->executeDarfPayment(
                $request->get('bank'),
                $request->validated(),
            ),
        );
    }

    public function index(DarfPaymentsIndexRequest $request): JsonResponse
    {
        return $this->execute(
            fn()
                => $this->banking->getDarfPayments(
                $request->get('bank'),
                $request->validated(),
            ),
        );
    }

}