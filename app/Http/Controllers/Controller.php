<?php

namespace App\Http\Controllers;

use App\Exceptions\AuthenticationFailedException;
use App\Exceptions\BankRequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;

abstract class Controller
{
    protected function execute(callable $callable): JsonResponse
    {
        try {
            return response()->json([
                'data' => $callable(),
            ]);
        } catch (ValidationException $e) {
            return response()->json(['message' => 'Validation failed', 'errors' => $e->errors()], 422);
        } catch (InvalidArgumentException $e) {
            return response()->json(['message' => $e->getMessage()], 422);
        } catch (AuthenticationFailedException $e) {
            return response()->json(['message' => 'Bank authentication failed.', 'error' => $e->getMessage()], 503);
        } catch (BankRequestException $e) {
            return response()->json(
                [
                    'message' => 'An error occurred with the bank API.',
                    'error' => $e->getMessage(),
                    'details' => $e->getDetails(),
                ],
                $e->getCode() ?: 503,
            );
        }
    }
}
