<?php

namespace App\Http\Controllers;

use App\Http\Requests\StatementRequestIndex;
use App\Services\Banking\BankingService;
use Illuminate\Http\JsonResponse;

class StatementController extends Controller
{
    public function __construct(private readonly BankingService $banking) {}

    public function index(StatementRequestIndex $request): JsonResponse
    {
        $validated = $request->validated();

        return $this->execute(
            fn()
                => $this->banking->getStatement(
                $validated['bank'],
                $validated['startDate'],
                $validated['endDate'],
            ),
        );
    }
}
