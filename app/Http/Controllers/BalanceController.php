<?php

namespace App\Http\Controllers;

use App\Http\Requests\BalanceRequestIndex;
use App\Services\Banking\BankingService;
use Illuminate\Http\JsonResponse;

class BalanceController extends Controller
{
    public function __construct(private readonly BankingService $banking) {}

    public function index(BalanceRequestIndex $request): JsonResponse
    {
        $validated = $request->validated();

        return $this->execute(
            fn()
                => $this->banking->getBalance(
                $validated['bank'],
                $validated['date'],
            ),
        );
    }
}
