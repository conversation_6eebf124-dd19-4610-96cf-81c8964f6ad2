<?php

namespace App\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class BoletoPaymentsIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'bank' => ['required', 'string', 'in:inter,bradesco,santander'],
            'barCode' => ['nullable', 'string', 'digits_between:44,48'],
            'transactionCode' => ['nullable', 'string', 'min:36'],
            'startDate' => ['required_with:endDate', 'date_format:Y-m-d'],
            'endDate' => ['required_with:startDate', 'date_format:Y-m-d', 'after_or_equal:startDate'],
            'filterby' => ['required_with:startDate', 'in:INCLUSAO,VENCIMENTO,PAGAMENTO'],
        ];
    }

    /**
     * Get the "after" validation callables for the request.
     *
     * @return array<int, \Closure>
     */
    public function after(): array
    {
        return [
            function (Validator $validator) {
                if ($validator->errors()->any()) {
                    return;
                }

                $startDate = Carbon::parse($this->input('startDate'));
                $endDate = Carbon::parse($this->input('endDate'));

                if ($startDate->diffInDays($endDate) > config('services.inter.query_max_date_interval')) {
                    $validator->errors()->add(
                        'endDate',
                        'Maximum date interval is 90 days.',
                    );
                }
            },
        ];
    }
}