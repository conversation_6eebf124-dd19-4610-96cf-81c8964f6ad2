<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PayPixRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'bank' => 'required|string|in:inter', // Assuming only inter for now
            'amount' => 'required|numeric|gt:0',
            'description' => 'nullable|string|max:140',
            'destination.keyType' => 'required|string|in:CPF,CNPJ,EMAIL,TELEFONE,CHAVE_ALEATORIA',
            'destination.key' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'destination.keyType.in' => 'The destination key type is invalid. Valid types are: CPF, CNPJ, EMAIL, TELEFONE, CHAVE_ALEATORIA.',
            'destination.key.required' => 'The destination PIX key is required.',
        ];
    }
}