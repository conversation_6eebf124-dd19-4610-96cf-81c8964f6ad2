<?php

namespace App\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class DarfPaymentsIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'bank' => 'required|string|in:inter',
            'solicitationCode' => 'nullable|numeric|digits:36',
            'receitaCode' => 'nullable|numeric|digits:4',
            'startDate' => 'nullable|date_format:Y-m-d',
            'endDate' => 'nullable|date_format:Y-m-d|after_or_equal:startDate',
        ];
    }

    public function after(): array
    {
        return [
            function (Validator $validator) {
                if ($validator->errors()->any()) {
                    return;
                }

                $startDate = Carbon::parse($this->input('startDate'));
                $endDate = Carbon::parse($this->input('endDate'));

                if ($startDate->diffInDays($endDate) > config('services.inter.query_max_date_interval')) {
                    $validator->errors()->add(
                        'endDate',
                        'Maximum date interval is 90 days.',
                    );
                }
            },
        ];
    }

}
