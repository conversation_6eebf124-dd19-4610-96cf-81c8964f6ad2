<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BoletoPaymentStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'bank' => ['bail', 'required', 'string', Rule::in(['inter', 'bradesco', 'santander'])],
            'barCode' => ['required','string','digits_between:44,48'],
            'amount' => ['required', 'numeric', 'min:0.01'],
            'paymentDate' => ['required', 'date_format:Y-m-d', 'after_or_equal:today'],
            'dueDate' => ['required', 'date_format:Y-m-d', 'after_or_equal:paymentDate'],
            'recipientsDocument' => ['required', 'string', 'digits:11'],
        ];
    }
}