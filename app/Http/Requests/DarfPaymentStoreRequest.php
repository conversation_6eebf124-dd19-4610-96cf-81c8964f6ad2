<?php

namespace App\Http\Requests;

use App\Rules\CpfOrCnpj;
use Illuminate\Foundation\Http\FormRequest;

class DarfPaymentStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'bank' => 'required|string|in:inter',
            'recipientsDocument' => ['required', new CpfOrCnpj],
            'receitaCode' => 'required|string|digits:4',
            'dueDate' => 'required|date_format:Y-m-d',
            'description' => 'required|string|max:1000',
            'companyName' => 'required|string|max:100',
            'companyPhone' => 'string|max:20',
            'assessmentPeriod' => 'required|date_format:Y-m-d',
            'amount' => 'required|numeric|gt:0',
            'feeValue' => 'numeric|gt:0',
            'interestValue' => 'numeric|gt:0',
            'reference' => 'required|string|max:30',
        ];
    }

}