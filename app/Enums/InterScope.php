<?php

namespace App\Enums;

/**
 * Enum com os escopos disponíveis na API do Banco Inter.
 * @see https://developers.bancointer.com.br/v4/docs/onde-comecar
 */
enum InterScope: string
{
    // Extrato
    case ExtratoRead = 'extrato.read';

    // Boletos
    case BoletoCobrancaRead = 'boleto-cobranca.read';
    case BoletoCobrancaWrite = 'boleto-cobranca.write';

    // Pagamentos
    case PagamentoBoletoWrite = 'pagamento-boleto.write';
    case PagamentoBoletoRead = 'pagamento-boleto.read';
    case PagamentoDarfWrite = 'pagamento-darf.write';
    case PagamentoPixWrite = 'pagamento-pix.write';
    case PagamentoPixRead = 'pagamento-pix.read';
    case PagamentoLoteWrite = 'pagamento-lote.write';
    case PagamentoLoteRead = 'pagamento-lote.read';

    // PIX Cobrança Imediata (COB)
    case CobWrite = 'cob.write';
    case CobRead = 'cob.read';

    // PIX Cobrança com Vencimento (COBV)
    case CobvWrite = 'cobv.write';
    case CobvRead = 'cobv.read';
    case LoteCobvRead = 'lotecobv.read';
    case LoteCobvWrite = 'lotecobv.write';

    // PIX Geral
    case PixWrite = 'pix.write';
    case PixRead = 'pix.read';

    // Webhooks
    case WebhookRead = 'webhook.read';
    case WebhookWrite = 'webhook.write';
    case WebhookBankingWrite = 'webhook-banking.write';
    case WebhookBankingRead = 'webhook-banking.read';

    // Payload Location
    case PayloadLocationWrite = 'payloadlocation.write';
    case PayloadLocationRead = 'payloadlocation.read';

    public function description(): string
    {
        return match ($this) {
            self::ExtratoRead => 'Consulta de Extrato e Saldo',
            self::BoletoCobrancaRead => 'Consulta de boletos e exportação para PDF',
            self::BoletoCobrancaWrite => 'Emissão e cancelamento de boletos',
            self::PagamentoBoletoWrite => 'Pagamento de titulo com código de barras',
            self::PagamentoBoletoRead => 'Obter dados completos do titulo a partir do código de barras ou da linha digitável',
            self::PagamentoDarfWrite => 'Pagamento de DARF sem código de barras',
            self::CobWrite => 'Emissão / alteração de pix cobrança imediata',
            self::CobRead => 'Consulta de pix cobrança imediata',
            self::CobvWrite => 'Emissão / alteração de pix cobrança com vencimento',
            self::CobvRead => 'Consulta de cobrança com vencimento',
            self::PixWrite => 'Solicitação de devolução de pix',
            self::PixRead => 'Consulta de pix',
            self::WebhookRead => 'Consulta do webhook',
            self::WebhookWrite => 'Alteração do webhook',
            self::PayloadLocationWrite => 'Criação de location do payload',
            self::PayloadLocationRead => 'Consulta de locations de payloads',
            self::PagamentoPixWrite => 'Pagamento de pix',
            self::PagamentoPixRead => 'Consulta de pix',
            self::WebhookBankingWrite => 'Alteração de webhooks da API Banking',
            self::WebhookBankingRead => 'Consulta do webhooks da API Banking',
            self::PagamentoLoteWrite => 'Realizar pagamentos em lotes',
            self::PagamentoLoteRead => 'Consultar lotes de pagamentos',
            self::LoteCobvRead => 'Consulta de lotes de cobranças com vencimento',
            self::LoteCobvWrite => 'Alteração de lotes de cobranças com vencimento',
        };
    }
}