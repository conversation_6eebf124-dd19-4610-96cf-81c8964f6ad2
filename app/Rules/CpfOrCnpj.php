<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CpfOrCnpj implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $digits = preg_replace('/\D/', '', $value);
        $length = strlen($digits);

        if ($length === 11) {
            if (!$this->isValidCpf($digits)) {
                $fail('The :attribute field is not a valid CPF.');
            }
        } elseif ($length === 14) {
            if (!$this->isValidCnpj($digits)) {
                $fail('The :attribute field is not a valid CNPJ.');
            }
        } else {
            $fail('The :attribute field must be a valid CPF or CNPJ.');
        }
    }


    private function isValidCpf(string $cpf): bool
    {
        // refuse if all digits are the same
        if (preg_match('/(\d)\1{10}/', $cpf)) {
            return false;
        }

        //calculate the first digit
        for ($i = 0, $j = 10, $sum = 0; $i < 9; $i++, $j--) {
            $sum += $cpf[$i] * $j;
        }
        $rest = $sum % 11;
        $digit1 = ($rest < 2) ? 0 : 11 - $rest;

        //calculate the second digit
        for ($i = 0, $j = 11, $sum = 0; $i < 10; $i++, $j--) {
            $sum += $cpf[$i] * $j;
        }
        $rest = $sum % 11;
        $digit2 = ($rest < 2) ? 0 : 11 - $rest;

        return $cpf[9] == $digit1 && $cpf[10] == $digit2;
    }

    private function isValidCnpj(string $cnpj): bool
    {
        // refuse if all digits are the same
        if (preg_match('/(\d)\1{13}/', $cnpj)) {
            return false;
        }

        //calculate the first digit
        $weights = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
        for ($i = 0, $sum = 0; $i < 12; $i++) {
            $sum += $cnpj[$i] * $weights[$i];
        }
        $rest = $sum % 11;
        $digit1 = ($rest < 2) ? 0 : 11 - $rest;

        //calculate the second digit
        $weights = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
        for ($i = 0, $sum = 0; $i < 13; $i++) {
            $sum += $cnpj[$i] * $weights[$i];
        }
        $rest = $sum % 11;
        $digit2 = ($rest < 2) ? 0 : 11 - $rest;

        return $cnpj[12] == $digit1 && $cnpj[13] == $digit2;
    }
}
