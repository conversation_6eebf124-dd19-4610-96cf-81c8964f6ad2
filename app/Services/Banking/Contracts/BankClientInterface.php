<?php

namespace App\Services\Banking\Contracts;

use App\Exceptions\BankRequestException;
use App\Exceptions\AuthenticationFailedException;
use PHPUnit\Event\Code\Throwable;

interface BankClientInterface
{
    /**
     * @return array<int, array<string, mixed>>
     * @throws BankRequestException
     * @throws AuthenticationFailedException
     * @throws Throwable
     */
    public function getBoletoCharges(array $filters): array;

    /**
     * @param array<string, mixed> $payload
     * @return array<string, mixed>
     * @throws BankRequestException
     * @throws AuthenticationFailedException
     * @throws Throwable
     */
    public function executeBoletoPayment(array $payload): array;

    /**
     * Returns the statement within a required time range
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function getStatement(string $startDate, string $endDate): array;

    /**
     * @param array<string, mixed> $payload
     * @return array<string, mixed>
     */
    public function executeDarfPayment(array $payload): array;

    /**
     * @param array<string, mixed> $filters
     * @return array<string, mixed>
     */
    public function getDarfPayment(array $filters): array;

    /**
     * @param array<string, mixed> $payload
     * @return array<string, mixed>
     */
    public function executePixPayment(array $payload): array;

    /**
     * @param array<string, mixed> $filters
     * @return array<string, mixed>
     */
    public function getPixPayments(array $filters): array;

    /**
     * @param string $transactionCode
     * @return mixed
     */
    public function cancelCharge(string $transactionCode);
}
