<?php

namespace App\Services\Banking;

use App\Exceptions\AuthenticationFailedException;
use App\Exceptions\BankRequestException;
use App\Services\Banking\Contracts\BankClientInterface;
use InvalidArgumentException;
use Illuminate\Support\Facades\Log;
use Throwable;

class BankingService
{
    /**
     * @var array<string, class-string<BankClientInterface>>
     */
    protected array $bankMap;

    /**
     * @param array<string, class-string<BankClientInterface>> $bankMap
     */
    public function __construct(array $bankMap)
    {
        $this->bankMap = $bankMap;
    }

    public function client(string $bank): BankClientInterface
    {
        $bank = strtolower($bank);
        if (!array_key_exists($bank, $this->bankMap)) {
            throw new InvalidArgumentException("Unsupported bank '$bank'.");
        }

        $class = $this->bankMap[$bank];
        return app($class);
    }

    /**
     * @param string $bank
     * @param array  $payload
     * @return array
     * @throws AuthenticationFailedException
     * @throws BankRequestException
     */
    public function executeBoletoPayment(string $bank, array $payload): array
    {
        return $this->client($bank)->executeBoletoPayment($payload);
    }

    /**
     * @param string $bank
     * @param        $filters
     * @return array
     * @throws AuthenticationFailedException
     * @throws BankRequestException
     */
    public function getBoletoPayments(string $bank, $filters): array
    {
        return $this->client($bank)->getBoletoCharges($filters);
    }

    /**
     * @param string $bank
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function getStatement(string $bank, string $startDate, string $endDate): array
    {
        return $this->client($bank)->getStatement($startDate, $endDate);
    }

    /**
     * @param string $bank
     * @param string $date
     * @return array
     */
    public function getBalance(string $bank, string $date): array
    {
        return $this->client($bank)->getBalance($date);
    }

    /**
     * @param string $bank
     * @param string $transactionCode
     * @return array
     */
    public function cancelBoletoPayment(string $bank, string $transactionCode): array
    {
        return $this->client($bank)->cancelCharge($transactionCode);
    }

    public function executeDarfPayment(string $bank, array $payload): array
    {
        return $this->client($bank)->executeDarfPayment($payload);
    }

    public function getDarfPayments(string $bank, array $filters): array
    {
        return $this->client($bank)->getDarfPayment($filters);
    }

    public function executePixPayment(string $bank, array $payload)
    {
        return $this->client($bank)->executePixPayment($payload);
    }

    public function getPixPayments(string $bank, array $filters)
    {
        return $this->client($bank)->getPixPayments($filters);
    }

}
