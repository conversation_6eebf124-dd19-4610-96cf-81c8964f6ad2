<?php

namespace App\Services\Banking\Banks;

use App\Services\Banking\Contracts\BankClientInterface;
use App\Exceptions\AuthenticationFailedException;
use App\Exceptions\InterApiException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class InterService implements BankClientInterface
{
    private array $config;

    private const array BOLETO_PAYMENT_FILTERS = [
        'barCode' => 'codBarraLinhaDigitavel',
        'transactionCode' => 'codigoTransacao',
        'startDate' => 'dataInicio',
        'endDate' => 'dataFim',
        'filterby' => 'filtrarDataPor',
    ];

    private const array DARF_PAYMENT_FILTERS = [
        'solicitationCode' => 'codigoSolicitacao',
        'receitaCode' => 'codigoReceita',
        'startDate' => 'dataInicio',
        'endDate' => 'dataFim',
    ];

    public function __construct()
    {
        $this->config = config('services.inter');
        $this->validateConfig();
    }

    public function getStatement(string $startDate, string $endDate): array
    {
        $response = $this->getHttpClient()->get(
            '/banking/v2/extrato',
            [
                'dataInicio' => $startDate,
                'dataFim' => $endDate,
            ],
        );

        if ($response->failed()) {
            throw new InterApiException($response, 'Failed to retrieve statement from Inter.');
        }

        return $response->json();
    }

    /**
     * @throws \App\Exceptions\InterApiException
     * @throws \App\Exceptions\AuthenticationFailedException
     * @throws \Illuminate\Http\Client\ConnectionException
     */
    public function getBalance(string $date): array
    {
        $response = $this->getHttpClient()->get(
            '/banking/v2/saldo',
            [
                'dataSaldo' => $date,
            ],
        );

        if ($response->failed()) {
            throw new InterApiException($response, 'Failed to retrieve balance from Inter.');
        }

        return $response->json();
    }

    public function cancelCharge(string $transactionCode): array
    {
        $response = $this->getHttpClient()->delete(
            '/banking/v2/pagamento/' . $transactionCode,
        );

        if ($response->failed()) {
            throw new InterApiException($response, 'Failed to cancel charge from Inter.');
        }

        return ['success' => true, 'message' => 'Charge cancelled successfully.'];
    }


    /**
     * Busca pagamentos já realizados ou agendados.
     *
     * @see https://developers.inter.co/references/banking#tag/Pagamento/operation/buscarInformacoesPagamentos
     * @throws InterApiException|AuthenticationFailedException|ConnectionException
     */
    public function getBoletoCharges(array $filters): array
    {
        $response = $this->getHttpClient()->get(
            '/banking/v2/pagamento',
            $this->cleanAndTranslateFilters(self::BOLETO_PAYMENT_FILTERS, $filters),
        );

        if ($response->failed()) {
            throw new InterApiException($response, 'Failed to retrieve payments from Inter.');
        }

        return $response->json();
    }

    private function cleanAndTranslateFilters(array $translation, array $filters): array
    {
        return collect($translation)
            ->mapWithKeys(fn(string $apiParam, string $appParam) => [$apiParam => $filters[$appParam] ?? null])
            ->filter()
            ->all();
    }

    /**
     * Executa o pagamento de um boleto.
     *
     * @see https://developers.inter.co/references/banking#tag/Pagamento/operation/pagarBoleto
     * @throws InterApiException|AuthenticationFailedException|ConnectionException
     */
    public function executeBoletoPayment(array $payload): array
    {
        $response = $this->getHttpClient()->post('/banking/v2/pagamento', [
            'codBarraLinhaDigitavel' => $payload['barCode'],
            'valorPagar' => $payload['amount'],
            'dataPagamento' => $payload['paymentDate'],
            'dataVencimento' => $payload['dueDate'],
            'cpfCnpjBeneficiario' => $payload['recipientsDocument'],
        ]);

        if ($response->failed()) {
            throw new InterApiException($response, 'Failed to execute boleto payment at Inter.');
        }

        return [
            'success' => true,
            'transaction_code' => $response->json('codigoTransacao'),
            'message' => 'Boleto payment scheduled/executed successfully.',
            'status' => $response->json('statusPagamento'),
            'response_payload' => $response->json(),
        ];
    }

    /**
     * @see https://developers.inter.co/references/banking#tag/Pagamento/operation/pagamentosDarf
     * @throws InterApiException
     * @throws AuthenticationFailedException
     * @throws ConnectionException
     */
    public function executeDarfPayment(array $payload): array
    {
        $response = $this->getHttpClient()->post('/banking/v2/pagamento/darf', [
            'cnpjCpf' => $payload['recipientsDocument'],
            'codigoReceita' => $payload['receitaCode'],
            'dataVencimento' => $payload['dueDate'],
            'descricao' => $payload['description'],
            'nomeEmpresa' => $payload['companyName'],
            'telefoneEmpresa' => $payload['companyPhone'] ?? null,
            'periodoApuracao' => $payload['assessmentPeriod'],
            'valorPrincipal' => $payload['amount'],
            'valorMulta' => $payload['feeValue'] ?? null,
            'valorJuros' => $payload['interestValue'] ?? null,
            'referencia' => $payload['reference'],
        ]);

        if ($response->failed()) {
            throw new InterApiException($response, 'Failed to execute darf payment at Inter.');
        }

        return [
            'success' => true,
            'code' => $response->json('codigoSolicitacao'),
            'message' => 'DARF payment scheduled/executed successfully.',
            'status' => $response->json('tipoRetorno'),
            'response_payload' => $response->json(),
        ];
    }

    /**
     * @see https://developers.inter.co/references/banking#tag/Pagamento/operation/buscarInformacoesPagamentoDarf
     * @param array $filters
     * @return array
     * @throws AuthenticationFailedException
     * @throws InterApiException
     * @throws ConnectionException
     */
    public function getDarfPayment(array $filters): array
    {
        $response = $this->getHttpClient()->get(
            '/banking/v2/pagamento/darf',
            $this->cleanAndTranslateFilters(self::DARF_PAYMENT_FILTERS, $filters),
        );

        if ($response->failed()) {
            throw new InterApiException($response, 'Failed to retrieve payments from Inter.');
        }

        return $response->json();
    }


    public function executePixPayment(array $payload): array
    {
        // TODO: Implement executePixPayment() method.
        return [];
    }

    public function getPixPayments(array $filters): array
    {
        // TODO: Implement getPixPayments() method.
        return [];
    }

    /**
     * @throws AuthenticationFailedException|ConnectionException
     */
    private function getHttpClient(): PendingRequest
    {
        return Http::withToken(
            $this->getAccessToken(),
        )
            ->withOptions([
                'cert' => $this->config['cert_path'],
                'ssl_key' => $this->config['key_path'],
            ])
            ->baseUrl($this->config['base_uri'])
            ->withHeaders([
                'x-inter-sdk' => 'laravel-http-client',
            ])
            ->acceptJson();
    }

    /**
     * @see https://developers.inter.co/references/token
     * @return string
     */
    private function getAccessToken(): string
    {
        $cacheKey = 'inter_api_token_' . $this->config['client_id'];

        // Tries to get the key on the cache first. If there is none, than the request is made
        return Cache::remember(
            $cacheKey,
            $this->config['token_cache_ttl'],
            function () {
                $response = Http::asForm()
                    ->withOptions([
                        'cert' => $this->config['cert_path'],
                        'ssl_key' => $this->config['key_path'],
                    ])
                    ->post($this->config['base_uri'] . '/oauth/v2/token', [
                        'client_id' => $this->config['client_id'],
                        'client_secret' => $this->config['client_secret'],
                        'grant_type' => 'client_credentials',
                        'scope' => $this->config['scope'],
                    ]);

                if ($response->failed()) {
                    Log::critical('Inter API - Failed to get access token', [
                        'status' => $response->status(),
                        'response' => $response->body(),
                    ]);
                    throw new AuthenticationFailedException(
                        'Could not authenticate with Inter API: ' . $response->body(),
                        $response->status(),
                        null,
                        $response,
                    );
                }

                return $response->json('access_token');
            },
        );
    }

    private function validateConfig(): void
    {
        $requiredKeys = ['base_uri', 'client_id', 'client_secret', 'cert_path', 'key_path'];
        foreach ($requiredKeys as $key) {
            if (empty($this->config[$key])) {
                throw new RuntimeException("Inter service config key '$key' is missing or empty.");
            }
        }
    }
}
