<?php

namespace App\Services\Banking\Banks;

use App\Services\Banking\Contracts\BankClientInterface;

class BradescoService implements BankClientInterface
{

    public function getBoletoCharges(array $filters): array
    {
        // TODO: Implement getBoletoCharges() method.
    }

    public function executeBoletoPayment(array $payload): array
    {
        // TODO: Implement executeBoletoPayment() method.
    }

    public function getStatement(string $startDate, string $endDate): array
    {
        // TODO: Implement getStatement() method.
    }

    public function executeDarfPayment(array $payload): array
    {
        // TODO: Implement executeDarfPayment() method.
    }

    public function getDarfPayment(array $filters): array
    {
        // TODO: Implement getDarfPayment() method.
    }

    public function executePixPayment(array $payload): array
    {
        // TODO: Implement executePixPayment() method.
    }

    public function getPixPayments(array $filters): array
    {
        // TODO: Implement getPixPayments() method.
    }

    public function cancelCharge(string $transactionCode)
    {
        // TODO: Implement cancelCharge() method.
    }
}
