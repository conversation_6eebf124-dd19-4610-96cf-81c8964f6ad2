<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMovement extends Model
{
    use HasFactory;

    protected $table = 'payment_movements';

    protected $fillable = [
        'bank',
        'amount',
        'reference',
        'request_payload',
        'response_payload',
        'success',
        'transaction_id',
        'message',
        'status',
        'occurred_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'request_payload' => 'array',
        'response_payload' => 'array',
        'success' => 'boolean',
        'occurred_at' => 'datetime',
    ];
}
