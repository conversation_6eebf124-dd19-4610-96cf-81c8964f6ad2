<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // API microservice: keep middleware minimal (stateless)
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Customize exception rendering if needed
    })
    ->withProviders([
        \App\Providers\AppServiceProvider::class,
        \App\Providers\BankingServiceProvider::class,
    ])->create();
